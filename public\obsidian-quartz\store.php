<?php
// Load configuration
require_once 'config.php';
require_once 'path-helper.php';

// Initialize paths
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Weird Items Store - A. A. Chips';
$meta_description = 'A collection of weird items I\'m selling, giving away, or wishing for. No checkout—just message me if interested.';
$meta_keywords = 'A. A. Chips, store, weird items, for sale, giveaway, wishlist';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];

// Load store items
$store_items_file = $paths['data_path'] . 'store-items.json';
$store_items = [];
if (file_exists($store_items_file)) {
    $json_content = file_get_contents($store_items_file);
    $store_data = json_decode($json_content, true);
    $store_items = $store_data['items'] ?? [];
}

// Post metadata for template compatibility
$post_data = array(
    'title' => 'Weird Items Store',
    'author' => 'A. A. Chips',
    'date' => date('Y-m-d'),
    'excerpt' => $meta_description,
    'tags' => array('store', 'items', 'weird'),
    'source_file' => 'store.php',
);

// Build content
ob_start();
?>

<div class="store-container">
    <h1>~/obsidian/store</h1>
    <p>A collection of weird items I'm selling, giving away, or wishing for. No checkout—just message me if interested.</p>
    
    <div class="store-nav">
        <button class="filter-btn active" data-filter="all">All Items</button>
        <button class="filter-btn" data-filter="sale">For Sale</button>
        <button class="filter-btn" data-filter="giveaway">Giveaway</button>
        <button class="filter-btn" data-filter="wishlist">Wishlist</button>
    </div>
    
    <div class="items-grid" id="items-grid">
        <!-- Items will be populated by JavaScript -->
    </div>
</div>



<script>
// Store items data
const storeItems = <?php echo json_encode($store_items); ?>;
const basePath = '<?php echo $base_url; ?>';
const imagePath = basePath + 'img/store/';

// Render items function
function renderItems(filter = 'all') {
    const grid = document.getElementById('items-grid');
    grid.innerHTML = '';
    
    const filteredItems = filter === 'all' ? storeItems : storeItems.filter(item => item.type === filter);
    
    filteredItems.forEach(item => {
        const itemCard = document.createElement('div');
        itemCard.className = `item-card ${item.status === 'Sold' ? 'sold' : ''} ${item.type}`;
        
        // Handle image
        const imageUrl = item.images && item.images.length > 0 
            ? imagePath + item.images[0] 
            : '';
        
        const imageHtml = imageUrl 
            ? `<img class="item-image" src="${imageUrl}" alt="${item.title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
               <div class="item-image placeholder" style="display:none;">📦 Image Coming Soon</div>`
            : `<div class="item-image placeholder">📦 Image Coming Soon</div>`;
        
        // Build price display
        let priceHtml = '';
        if (item.price) {
            if (typeof item.price === 'number') {
                priceHtml = `<div class="item-price">$${item.price.toFixed(2)}</div>`;
            } else {
                priceHtml = `<div class="item-price">${item.price}</div>`;
            }
        }
        
        // Contact button for available items
        const contactHtml = item.status === 'Available' 
            ? `<a class="contact-btn" href="mailto:<EMAIL>?subject=Interest in ${encodeURIComponent(item.title)}">Message me</a>`
            : '';
        
        itemCard.innerHTML = `
            ${imageHtml}
            <h3 class="item-title">
                <a href="${basePath}store/${item.slug}.php">${item.title}</a>
            </h3>
            <div class="item-description">${item.description}</div>
            ${priceHtml}
            ${item.condition ? `<div class="item-condition">Condition: ${item.condition}</div>` : ''}
            ${contactHtml}
        `;
        
        grid.appendChild(itemCard);
    });
    
    if (filteredItems.length === 0) {
        grid.innerHTML = '<div style="text-align: center; color: var(--text-muted, #666); padding: 40px;">No items found in this category.</div>';
    }
}

// Filter button event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            renderItems(btn.dataset.filter);
        });
    });
    
    // Initial render
    renderItems();
});
</script>

<?php
$content = ob_get_clean();

// Add store-specific CSS
$additional_css = '<link rel="stylesheet" href="' . ($css_path ?? 'css/') . 'store.css?v=' . time() . '">';

// Include the template
include 'page template.htm';
?>
