{"main": {"id": "34ed48dade05a336", "type": "split", "children": [{"id": "a8fcd3b9f6ad8b81", "type": "tabs", "children": [{"id": "9e7e3935dd209c64", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Rainbow Gatherings and Radical Christianity.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Rainbow Gatherings and Radical Christianity"}}, {"id": "ade3e07597fe4be7", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Untitled.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled"}}, {"id": "d50f175a1e4b954e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "content/Untitled.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled"}}, {"id": "3d070dbafa777893", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Site changes.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Site changes"}}, {"id": "6896e68304bdc27a", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Untitled.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "Untitled"}}], "currentTab": 4}], "direction": "vertical"}, "left": {"id": "4036a82dddf209cb", "type": "split", "children": [{"id": "0b2607ccdc5686e6", "type": "tabs", "children": [{"id": "933f46e87182aefd", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "225f2d1c63aa7d23", "type": "leaf", "state": {"type": "search", "state": {"query": "wonderful", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "62b3926c58756e42", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 338.5}, "right": {"id": "10673cb66139e182", "type": "split", "children": [{"id": "72803710758f1f5e", "type": "tabs", "children": [{"id": "2ec70085debaa449", "type": "leaf", "state": {"type": "backlink", "state": {"file": "content/images.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for images"}}, {"id": "9a3f53ff23da177f", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "content/images.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from images"}}, {"id": "e13f9948df9976a8", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "57a7f573390b2537", "type": "leaf", "state": {"type": "outline", "state": {"file": "content/images.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of images"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false, "obsidian-excalidraw-plugin:New drawing": false}}, "active": "6896e68304bdc27a", "lastOpenFiles": ["content/Maryella Chronicles - <PERSON>.md", "content/Untitled 2.md", "content/Steps for Mary.md", "content/I wish the big TV would come back on...md", "content/I wish.md", "content/Untitled 7.md", "migrate-images.php", "content/test-dynamic-images.php", "manual-test.php", "test-build.php", "content/inspiration/I wish you enough.php", "test-json.php", "test-image-helper.php", "content/Family Tree Tom Chapin.php", "content/test-dynamic-images.md", "IMAGE_REFERENCE_GUIDE.md", "includes/image-helper.php", "js/image-resolver.js", "Site changes.md", "Untitled.md", "content/Untitled 6.md", "content/Untitled 5.md", "content/Untitled 4.md", "content/Untitled 3.md", "content/inspiration/I wish you enough.md", "content/I wish you enough.md", "content/Untitled 1.md", "content/index.md", "content/Family Tree Tom Chapin.md", "CATEGORY_CREATION_README.md", "content/kitchen/ingredient-wishlist.md", "content/bday-wishlist.md", "content/music/Music Section Index.md", "content/inspiration/Happiness - Animated Film - Rats.md", "content/dreamers-blessing.md", "content/Personal Blog_ Choose Your Own Adventure Outline.md", "img/zucchyini eyes-fotor-ai-art-effects-20250711210258.jpg", "img/self/masquerade.jpg", "img/masquerade.jpg", "content/playlists/assets/thumbs/petermayer.jpg", "content/playlists/assets/thumbs/loritruedavidhaas.jpg", "content/playlists/assets/thumbs/gooutwithjoy.jpg", "content/playlists/assets/folkwomen/Pasted image 20250510215407.png", "content/playlists/assets/thumbs/aaronshneyer.jpg", "content/playlists/assets/lyrics/IMG_20200106_224042217.jpg", "content/playlists/assets/mp3/jewishnotzionist/IMG_20200106_224042217.jpg"]}