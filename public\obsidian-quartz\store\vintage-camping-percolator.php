<?php
// Individual item page template
// This file should be copied and renamed for each item

// Load configuration
require_once '../config.php';
require_once '../path-helper.php';

// Initialize paths
$paths = initPaths($config, __FILE__);

// Get item slug from filename
$current_file = basename($_SERVER['PHP_SELF'], '.php');
$item_slug = $current_file;

// Load store items
$store_items_file = $paths['data_path'] . 'store-items.json';
$item_data = null;

if (file_exists($store_items_file)) {
    $json_content = file_get_contents($store_items_file);
    $store_data = json_decode($json_content, true);
    $items = $store_data['items'] ?? [];
    
    // Find the item by slug
    foreach ($items as $item) {
        if ($item['slug'] === $item_slug) {
            $item_data = $item;
            break;
        }
    }
}

// If item not found, redirect to store
if (!$item_data) {
    header('Location: ../store.php');
    exit;
}

// Page variables
$page_title = $item_data['title'] . ' - Weird Items Store - A. A. Chips';
$meta_description = $item_data['description'];
$meta_keywords = 'A. A. Chips, store, ' . $item_data['title'] . ', ' . $item_data['type'];
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];

// Post metadata for template compatibility
$post_data = array(
    'title' => $item_data['title'],
    'author' => 'A. A. Chips',
    'date' => $item_data['date_listed'],
    'excerpt' => $item_data['description'],
    'tags' => array('store', $item_data['type']),
    'source_file' => 'store/' . $item_slug . '.php',
);

// Build breadcrumb
$breadcrumb = [
    ['title' => 'Home', 'url' => $base_url . 'index.php'],
    ['title' => 'Store', 'url' => $base_url . 'store.php'],
    ['title' => $item_data['title']]
];

// Build content
ob_start();
?>

<div class="item-detail-container">
    <div class="item-header">
        <div class="item-status-badges">
            <?php if ($item_data['type'] === 'wishlist'): ?>
                <span class="status-badge wishlist">🌟 WISHLIST</span>
            <?php elseif ($item_data['type'] === 'giveaway'): ?>
                <span class="status-badge giveaway">🎁 FREE</span>
            <?php endif; ?>
            
            <?php if ($item_data['status'] === 'Sold'): ?>
                <span class="status-badge sold">☠️ SOLD</span>
            <?php endif; ?>
        </div>
        
        <h1><?php echo htmlspecialchars($item_data['title']); ?></h1>
    </div>
    
    <div class="item-content">
        <div class="item-images">
            <?php if (!empty($item_data['images'])): ?>
                <?php foreach ($item_data['images'] as $index => $image): ?>
                    <img src="<?php echo $base_url . 'img/store/' . $image; ?>" 
                         alt="<?php echo htmlspecialchars($item_data['title']); ?>" 
                         class="item-image <?php echo $index === 0 ? 'main-image' : 'thumbnail'; ?>"
                         onerror="this.style.display='none';">
                <?php endforeach; ?>
            <?php else: ?>
                <div class="item-image placeholder main-image">📦 Image Coming Soon</div>
            <?php endif; ?>
        </div>
        
        <div class="item-details">
            <div class="item-description">
                <p><?php echo nl2br(htmlspecialchars($item_data['description'])); ?></p>
            </div>
            
            <?php if ($item_data['price']): ?>
                <div class="item-price <?php echo $item_data['status'] === 'Sold' ? 'sold' : ''; ?>">
                    <?php 
                    if (is_numeric($item_data['price'])) {
                        echo '$' . number_format($item_data['price'], 2);
                    } else {
                        echo htmlspecialchars($item_data['price']);
                    }
                    ?>
                </div>
            <?php endif; ?>
            
            <div class="item-metadata">
                <?php if ($item_data['condition']): ?>
                    <div class="metadata-item">
                        <strong>Condition:</strong> <?php echo htmlspecialchars($item_data['condition']); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($item_data['weight_lbs']): ?>
                    <div class="metadata-item">
                        <strong>Weight:</strong> <?php echo $item_data['weight_lbs']; ?> lbs
                    </div>
                <?php endif; ?>
                
                <div class="metadata-item">
                    <strong>Listed:</strong> <?php echo date('F j, Y', strtotime($item_data['date_listed'])); ?>
                </div>
            </div>
            
            <?php if ($item_data['acquisition_story']): ?>
                <div class="acquisition-story">
                    <h3>The Story</h3>
                    <p><?php echo nl2br(htmlspecialchars($item_data['acquisition_story'])); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($item_data['status'] === 'Available'): ?>
                <div class="contact-section">
                    <a href="mailto:<EMAIL>?subject=Interest in <?php echo urlencode($item_data['title']); ?>&body=Hi! I'm interested in the <?php echo urlencode($item_data['title']); ?>. <?php echo $item_data['type'] === 'wishlist' ? 'Can you help me find this item?' : 'Is it still available?'; ?>" 
                       class="contact-btn">
                        <?php echo $item_data['type'] === 'wishlist' ? 'Help me find this!' : 'Message me about this item'; ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="navigation-links">
        <a href="<?php echo $base_url; ?>store.php" class="back-to-store">← Back to Store</a>
    </div>
</div>

<style>
.item-detail-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.item-header {
    margin-bottom: 30px;
}

.item-status-badges {
    margin-bottom: 15px;
}

.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    margin-right: 10px;
}

.status-badge.wishlist {
    background: rgba(255, 170, 0, 0.2);
    color: #ffaa00;
    border: 1px solid #ffaa00;
}

.status-badge.giveaway {
    background: rgba(68, 255, 68, 0.2);
    color: #44ff44;
    border: 1px solid #44ff44;
}

.status-badge.sold {
    background: rgba(255, 68, 68, 0.2);
    color: #ff4444;
    border: 1px solid #ff4444;
}

.item-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 30px;
}

.item-images {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.item-image {
    width: 100%;
    border-radius: 5px;
    border: 1px solid var(--border-color, #333);
}

.main-image {
    height: 300px;
    object-fit: cover;
}

.thumbnail {
    height: 80px;
    object-fit: cover;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.thumbnail:hover {
    opacity: 1;
}

.item-image.placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary, #1a1a1a);
    color: var(--text-muted, #666);
    font-size: 1.2em;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.item-description {
    line-height: 1.6;
    color: var(--text-secondary, #aaa);
}

.item-price {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--accent-color, #00ff00);
}

.item-price.sold {
    text-decoration: line-through;
    color: #888;
}

.item-metadata {
    background: var(--bg-secondary, #1a1a1a);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid var(--border-color, #333);
}

.metadata-item {
    margin-bottom: 8px;
    font-size: 0.9em;
}

.metadata-item:last-child {
    margin-bottom: 0;
}

.metadata-item strong {
    color: var(--text-primary, #ccc);
}

.acquisition-story {
    background: var(--bg-secondary, #1a1a1a);
    padding: 20px;
    border-radius: 5px;
    border-left: 3px solid var(--accent-color, #00ff00);
}

.acquisition-story h3 {
    margin: 0 0 10px 0;
    color: var(--accent-color, #00ff00);
    font-size: 1.1em;
}

.acquisition-story p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-secondary, #aaa);
    font-style: italic;
}

.contact-section {
    text-align: center;
    padding: 20px;
    background: var(--bg-secondary, #1a1a1a);
    border-radius: 5px;
    border: 1px solid var(--accent-color, #00ff00);
}

.contact-btn {
    display: inline-block;
    background: var(--accent-color, #00ff00);
    color: var(--bg-primary, #000);
    padding: 12px 24px;
    text-decoration: none;
    font-family: var(--font-mono, 'Courier New', monospace);
    font-weight: bold;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background: var(--text-primary, #fff);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
    transform: translateY(-2px);
}

.navigation-links {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--border-color, #333);
}

.back-to-store {
    color: var(--accent-color, #00ff00);
    text-decoration: none;
    font-family: var(--font-mono, 'Courier New', monospace);
}

.back-to-store:hover {
    text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

@media (max-width: 768px) {
    .item-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .main-image {
        height: 250px;
    }
}
</style>

<script>
// Image gallery functionality
document.addEventListener('DOMContentLoaded', function() {
    const mainImage = document.querySelector('.main-image');
    const thumbnails = document.querySelectorAll('.thumbnail');
    
    thumbnails.forEach(thumb => {
        thumb.addEventListener('click', function() {
            if (mainImage && this.src) {
                const tempSrc = mainImage.src;
                mainImage.src = this.src;
                this.src = tempSrc;
            }
        });
    });
});
</script>

<?php
$content = ob_get_clean();

// Include the template
include '../page template.htm';
?>
